@extends('layouts.contentNavbarLayout')

@section('title', 'Data Pendapatan')

@section('page-style')
    <style>
        .modern-container {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .page-header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            border-radius: 20px;
            padding: 2.5rem;
            color: white;
            margin-bottom: 2.5rem;
            box-shadow: 0 20px 40px rgba(79, 70, 229, 0.15);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            pointer-events: none;
        }

        .stat-card {
            border: none;
            border-radius: 16px;
            background: white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            position: relative;
            height: 100%;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4f46e5, #7c3aed);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .stat-icon {
            width: 56px;
            height: 56px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.75rem;
            margin-right: 1.25rem;
            flex-shrink: 0;
        }

        .filter-card {
            background: white;
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
            margin-bottom: 2rem;
            border: 1px solid rgba(79, 70, 229, 0.1);
        }

        .search-input {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 0.875rem 1.25rem;
            font-size: 0.875rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: #fafbfc;
        }

        .search-input:focus {
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            outline: none;
            background: white;
        }

        .btn-modern {
            border-radius: 12px;
            padding: 0.875rem 1.75rem;
            font-weight: 600;
            border: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.875rem;
        }

        .btn-primary-modern {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        .btn-primary-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
        }

        .btn-outline-modern {
            border: 2px solid #e5e7eb;
            background: white;
            color: #6b7280;
        }

        .btn-outline-modern:hover {
            background: #f9fafb;
            border-color: #4f46e5;
            color: #4f46e5;
            transform: translateY(-1px);
        }

        .data-table {
            background: white;
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            border: 1px solid rgba(79, 70, 229, 0.1);
        }

        .table-modern {
            margin-bottom: 0;
            font-size: 0.875rem;
        }

        .table-modern thead th {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: none;
            padding: 1.25rem 1rem;
            font-weight: 700;
            color: #374151;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 2px solid #e5e7eb;
        }

        .table-modern tbody td {
            padding: 1.25rem 1rem;
            border-top: 1px solid #f3f4f6;
            vertical-align: middle;
            color: #374151;
            font-weight: 500;
        }

        .table-modern tbody tr {
            transition: all 0.2s ease;
        }

        .table-modern tbody tr:hover {
            background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
            transform: scale(1.001);
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-paid {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border: 1px solid #10b981;
        }

        .status-pending {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border: 1px solid #f59e0b;
        }

        .status-overdue {
            background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
            color: #991b1b;
            border: 1px solid #ef4444;
        }

        .action-btn {
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            border: 2px solid transparent;
            font-size: 0.75rem;
            margin: 0 0.25rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .pagination-modern .page-link {
            border: none;
            border-radius: 10px;
            margin: 0 0.25rem;
            color: #6b7280;
            padding: 0.75rem 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .pagination-modern .page-link:hover {
            background: #f3f4f6;
            color: #4f46e5;
            transform: translateY(-1px);
        }

        .pagination-modern .page-item.active .page-link {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            border-color: transparent;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            border-radius: 16px;
            backdrop-filter: blur(4px);
        }

        .spinner {
            width: 2.5rem;
            height: 2.5rem;
            border: 3px solid #e5e7eb;
            border-top: 3px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .no-data {
            text-align: center;
            padding: 4rem 2rem;
            color: #6b7280;
        }

        .no-data i {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            opacity: 0.4;
            color: #9ca3af;
        }

        .no-data h6 {
            color: #374151;
            font-weight: 600;
        }

        /* Enhanced responsive design */
        @media (max-width: 1200px) {
            .modern-container {
                padding: 1.5rem 0;
            }
        }

        @media (max-width: 768px) {
            .page-header {
                padding: 2rem 1.5rem;
                text-align: center;
                margin-bottom: 2rem;
            }

            .page-header h2 {
                font-size: 1.75rem;
            }

            .stat-card {
                margin-bottom: 1.5rem;
            }

            .stat-icon {
                width: 48px;
                height: 48px;
                font-size: 1.5rem;
            }

            .filter-card {
                margin-bottom: 1.5rem;
            }

            .filter-card .row>div {
                margin-bottom: 1rem;
            }

            .table-responsive {
                border-radius: 16px;
            }

            .table-modern thead th {
                padding: 1rem 0.75rem;
                font-size: 0.75rem;
            }

            .table-modern tbody td {
                padding: 1rem 0.75rem;
                font-size: 0.8rem;
            }

            .action-btn {
                padding: 0.375rem 0.5rem;
                margin: 0 0.125rem;
            }

            .btn-modern {
                padding: 0.75rem 1.25rem;
                font-size: 0.8rem;
            }
        }

        @media (max-width: 576px) {
            .modern-container {
                padding: 1rem 0;
            }

            .page-header {
                padding: 1.5rem 1rem;
                margin-bottom: 1.5rem;
            }

            .page-header h2 {
                font-size: 1.5rem;
            }

            .stat-card .card-body {
                padding: 1.5rem;
            }

            .filter-card .card-body {
                padding: 1.5rem;
            }

            .data-table .card-header {
                padding: 1.5rem;
            }

            .table-modern thead th {
                padding: 0.75rem 0.5rem;
                font-size: 0.7rem;
            }

            .table-modern tbody td {
                padding: 0.75rem 0.5rem;
                font-size: 0.75rem;
            }

            .no-data {
                padding: 2rem 1rem;
            }

            .no-data i {
                font-size: 3rem;
            }
        }

        /* Additional modern enhancements */
        .card-header-modern {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-bottom: 2px solid #e5e7eb;
        }

        .text-gradient {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .shadow-modern {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
        }

        .border-modern {
            border: 1px solid rgba(79, 70, 229, 0.1);
        }
    </style>
@endsection

@section('content')
    <div class="modern-container">
        <div class="row">
            <!-- Page Header -->
            <div class="page-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center mb-3">
                            <div class="stat-icon bg-white bg-opacity-20 text-white me-3">
                                <i class="bx bx-chart-line"></i>
                            </div>
                            <div>
                                <h2 class="mb-1 fw-bold">Data Pendapatan</h2>
                                <p class="mb-0 opacity-90">Kelola dan pantau pendapatan dari invoice pelanggan</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-md-end mt-3 mt-md-0">
                        <div class="d-flex align-items-center justify-content-md-end">
                            <div class="stat-icon bg-white bg-opacity-20 text-white me-3">
                                <i class="bx bx-receipt"></i>
                            </div>
                            <div>
                                <small class="d-block opacity-90 fw-medium">Total Invoice</small>
                                <strong class="fs-4">{{ number_format($totalInvoices ?? 0, 0, ',', '.') }}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 col-12 mb-3">
                    <div class="card stat-card h-100">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-success bg-opacity-10 text-success">
                                    <i class="bx bx-money"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <p class="text-muted mb-1 fw-medium">Total Pendapatan</p>
                                    <h4 class="mb-0 fw-bold text-dark">Rp
                                        {{ number_format($totalRevenue ?? 0, 0, ',', '.') }}</h4>
                                    <small class="text-success">
                                        <i class="bx bx-trending-up me-1"></i>Semua waktu
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 col-12 mb-3">
                    <div class="card stat-card h-100">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-primary bg-opacity-10 text-primary">
                                    <i class="bx bx-calendar"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <p class="text-muted mb-1 fw-medium">Bulan Ini</p>
                                    <h4 class="mb-0 fw-bold text-dark">Rp
                                        {{ number_format($monthlyRevenue ?? 0, 0, ',', '.') }}</h4>
                                    <small class="text-primary">
                                        <i class="bx bx-calendar-check me-1"></i>{{ date('F Y') }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 col-12 mb-3">
                    <div class="card stat-card h-100">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-warning bg-opacity-10 text-warning">
                                    <i class="bx bx-time-five"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <p class="text-muted mb-1 fw-medium">Belum Bayar</p>
                                    <h4 class="mb-0 fw-bold text-dark">Rp
                                        {{ number_format($pendingRevenue ?? 0, 0, ',', '.') }}</h4>
                                    <small class="text-warning">
                                        <i class="bx bx-hourglass me-1"></i>Menunggu
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 col-12 mb-3">
                    <div class="card stat-card h-100">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-info bg-opacity-10 text-info">
                                    <i class="bx bx-receipt"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <p class="text-muted mb-1 fw-medium">Total Invoice</p>
                                    <h4 class="mb-0 fw-bold text-dark">{{ number_format($totalInvoices ?? 0, 0, ',', '.') }}
                                    </h4>
                                    <small class="text-info">
                                        <i class="bx bx-file me-1"></i>Dokumen
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter and Search -->
            <div class="card filter-card">
                <div class="card-body p-4">
                    <div class="row align-items-end">
                        <div class="col-lg-4 col-md-6 mb-3 mb-lg-0">
                            <label class="form-label fw-medium text-dark">Pencarian</label>
                            <div class="position-relative">
                                <input type="text" id="searchInput" class="form-control search-input"
                                    placeholder="Cari nama pelanggan atau paket..." value="{{ $search ?? '' }}">
                                <i class="bx bx-search position-absolute"
                                    style="right: 12px; top: 50%; transform: translateY(-50%); color: #6c757d;"></i>
                            </div>
                        </div>

                        <div class="col-lg-2 col-md-6 mb-3 mb-lg-0">
                            <label class="form-label fw-medium text-dark">Status</label>
                            <select id="statusFilter" class="form-select search-input">
                                <option value="">Semua Status</option>
                                @foreach ($statusOptions ?? [] as $status)
                                    <option value="{{ $status->id }}"
                                        {{ ($status->id ?? '') == $status->id ? 'selected' : '' }}>
                                        {{ $status->nama_status }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-lg-2 col-md-6 mb-3 mb-lg-0">
                            <label class="form-label fw-medium text-dark">Dari Tanggal</label>
                            <input type="date" id="startDate" class="form-control search-input"
                                value="{{ $startDate ?? '' }}">
                        </div>

                        <div class="col-lg-2 col-md-6 mb-3 mb-lg-0">
                            <label class="form-label fw-medium text-dark">Sampai Tanggal</label>
                            <input type="date" id="endDate" class="form-control search-input"
                                value="{{ $endDate ?? '' }}">
                        </div>

                        <div class="col-lg-2 col-md-12">
                            <div class="d-flex gap-2">
                                <button type="button" id="filterBtn" class="btn btn-modern btn-primary-modern flex-fill">
                                    <i class="bx bx-filter-alt me-1"></i>Filter
                                </button>
                                <button type="button" id="resetBtn" class="btn btn-modern btn-outline-modern">
                                    <i class="bx bx-refresh"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Table -->
            <div class="card data-table position-relative">
                <div id="loadingOverlay" class="loading-overlay d-none">
                    <div class="spinner"></div>
                </div>

                <div class="card-header border-0 card-header-modern p-4">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-primary bg-opacity-10 text-primary me-3">
                                    <i class="bx bx-table"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1 fw-bold text-gradient">Daftar Pendapatan</h5>
                                    <p class="text-muted mb-0 fw-medium">Data invoice dan pembayaran pelanggan</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 text-md-end mt-3 mt-md-0">
                            <div class="d-flex align-items-center justify-content-md-end">
                                <div class="badge bg-light text-dark border px-3 py-2">
                                    <i class="bx bx-info-circle me-1"></i>
                                    <span class="fw-medium">{{ $invoices->count() ?? 0 }} dari
                                        {{ $invoices->total() ?? 0 }} data</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-modern" id="dataTable">
                            <thead>
                                <tr>
                                    <th class="sortable" data-sort="index">
                                        <i class="bx bx-hash me-1"></i>No
                                        <i class="bx bx-chevron-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="date">
                                        <i class="bx bx-calendar me-1"></i>Tanggal
                                        <i class="bx bx-chevron-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="customer">
                                        <i class="bx bx-user me-1"></i>Pelanggan
                                        <i class="bx bx-chevron-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="package">
                                        <i class="bx bx-package me-1"></i>Paket
                                        <i class="bx bx-chevron-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="amount">
                                        <i class="bx bx-money me-1"></i>Jumlah
                                        <i class="bx bx-chevron-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="due_date">
                                        <i class="bx bx-time me-1"></i>Jatuh Tempo
                                        <i class="bx bx-chevron-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="status">
                                        <i class="bx bx-check-circle me-1"></i>Status
                                        <i class="bx bx-chevron-up sort-icon"></i>
                                    </th>
                                    <th>
                                        <i class="bx bx-cog me-1"></i>Aksi
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="tableBody">
                                @forelse($invoices ?? [] as $index => $invoice)
                                    <tr>
                                        <td class="fw-medium">{{ $invoices->firstItem() + $index }}</td>
                                        <td>{{ \Carbon\Carbon::parse($invoice->created_at)->format('d/m/Y') }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="stat-icon bg-primary bg-opacity-10 text-primary me-2"
                                                    style="width: 32px; height: 32px; font-size: 0.875rem;">
                                                    <i class="bx bx-user"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold text-dark">
                                                        {{ $invoice->customer->nama_customer ?? 'N/A' }}</div>
                                                    <small
                                                        class="text-muted">{{ Str::limit($invoice->customer->alamat ?? '', 35) }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-gradient-primary text-white px-3 py-2 rounded-pill">
                                                <i
                                                    class="bx bx-package me-1"></i>{{ $invoice->paket->nama_paket ?? 'N/A' }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="stat-icon bg-success bg-opacity-10 text-success me-2"
                                                    style="width: 32px; height: 32px; font-size: 0.875rem;">
                                                    <i class="bx bx-money"></i>
                                                </div>
                                                <div class="fw-bold text-success fs-6">Rp
                                                    {{ number_format($invoice->tagihan, 0, ',', '.') }}</div>
                                            </div>
                                        </td>
                                        <td>{{ \Carbon\Carbon::parse($invoice->jatuh_tempo)->format('d/m/Y') }}</td>
                                        <td>
                                            @php
                                                $statusClass = 'status-pending';
                                                if ($invoice->status->nama_status == 'Sudah Bayar') {
                                                    $statusClass = 'status-paid';
                                                } elseif (\Carbon\Carbon::parse($invoice->jatuh_tempo)->isPast()) {
                                                    $statusClass = 'status-overdue';
                                                }
                                            @endphp
                                            <span class="status-badge {{ $statusClass }}">
                                                {{ $invoice->status->nama_status ?? 'N/A' }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-2 justify-content-center">
                                                <button class="btn btn-sm btn-outline-primary action-btn shadow-sm"
                                                    onclick="viewInvoice({{ $invoice->id }})" title="Lihat Detail"
                                                    data-bs-toggle="tooltip">
                                                    <i class="bx bx-show me-1"></i>
                                                    <span class="d-none d-md-inline">Detail</span>
                                                </button>
                                                @if ($invoice->status->nama_status != 'Sudah Bayar')
                                                    <a href="{{ route('payment.show', $invoice->id) }}"
                                                        class="btn btn-sm btn-outline-success action-btn shadow-sm"
                                                        title="Bayar" data-bs-toggle="tooltip">
                                                        <i class="bx bx-credit-card me-1"></i>
                                                        <span class="d-none d-md-inline">Bayar</span>
                                                    </a>
                                                @endif
                                                <button class="btn btn-sm btn-outline-info action-btn shadow-sm"
                                                    onclick="printInvoice({{ $invoice->id }})" title="Cetak"
                                                    data-bs-toggle="tooltip">
                                                    <i class="bx bx-printer me-1"></i>
                                                    <span class="d-none d-md-inline">Cetak</span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="no-data">
                                            <i class="bx bx-inbox"></i>
                                            <h6 class="mt-2 mb-1">Tidak ada data</h6>
                                            <p class="mb-0">Belum ada data pendapatan yang tersedia</p>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>

                @if ($invoices->hasPages())
                    <div class="card-footer border-0 bg-white p-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                Menampilkan {{ $invoices->firstItem() ?? 0 }} - {{ $invoices->lastItem() ?? 0 }}
                                dari {{ $invoices->total() ?? 0 }} data
                            </div>
                            <nav>
                                {{ $invoices->appends(request()->query())->links('pagination::bootstrap-4', ['class' => 'pagination-modern']) }}
                            </nav>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@section('page-script')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize variables
            let searchTimeout;
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');
            const startDate = document.getElementById('startDate');
            const endDate = document.getElementById('endDate');
            const filterBtn = document.getElementById('filterBtn');
            const resetBtn = document.getElementById('resetBtn');
            const loadingOverlay = document.getElementById('loadingOverlay');
            const tableBody = document.getElementById('tableBody');

            // Real-time search functionality
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    performSearch();
                }, 500); // Delay 500ms untuk menghindari terlalu banyak request
            });

            // Filter button click
            filterBtn.addEventListener('click', function() {
                performSearch();
            });

            // Reset button click
            resetBtn.addEventListener('click', function() {
                searchInput.value = '';
                statusFilter.value = '';
                startDate.value = '';
                endDate.value = '';
                performSearch();
            });

            // Status filter change
            statusFilter.addEventListener('change', function() {
                performSearch();
            });

            // Date filter change
            startDate.addEventListener('change', function() {
                performSearch();
            });

            endDate.addEventListener('change', function() {
                performSearch();
            });

            // Perform search function
            function performSearch() {
                showLoading();

                const searchParams = new URLSearchParams({
                    search: searchInput.value,
                    status: statusFilter.value,
                    start_date: startDate.value,
                    end_date: endDate.value
                });

                // Update URL without page reload
                const newUrl = `${window.location.pathname}?${searchParams.toString()}`;
                window.history.pushState({}, '', newUrl);

                // Perform AJAX request
                fetch(`{{ route('pendapatan.ajax') }}?${searchParams.toString()}`, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        hideLoading();
                        updateTable(data.html);
                        updateStatistics(); // Update statistics if needed
                    })
                    .catch(error => {
                        hideLoading();
                        console.error('Error:', error);
                        showNotification('Terjadi kesalahan saat memuat data', 'error');
                    });
            }

            // Show loading overlay
            function showLoading() {
                loadingOverlay.classList.remove('d-none');
            }

            // Hide loading overlay
            function hideLoading() {
                loadingOverlay.classList.add('d-none');
            }

            // Update table content
            function updateTable(html) {
                const tableContainer = document.querySelector('.table-responsive');
                if (tableContainer && html) {
                    tableContainer.innerHTML = html;
                    initializeTableSorting(); // Re-initialize sorting after update
                }
            }

            // Update statistics (optional - if you want real-time stats update)
            function updateStatistics() {
                // This can be implemented to update the statistics cards
                // based on current filter results
            }

            // Table sorting functionality
            function initializeTableSorting() {
                const sortableHeaders = document.querySelectorAll('.sortable');

                sortableHeaders.forEach(header => {
                    header.addEventListener('click', function() {
                        const sortField = this.dataset.sort;
                        const currentSort = this.classList.contains('sort-asc') ? 'asc' : 'desc';
                        const newSort = currentSort === 'asc' ? 'desc' : 'asc';

                        // Remove sort classes from all headers
                        sortableHeaders.forEach(h => {
                            h.classList.remove('sort-asc', 'sort-desc');
                            const icon = h.querySelector('.sort-icon');
                            if (icon) {
                                icon.className = 'bx bx-chevron-up sort-icon';
                            }
                        });

                        // Add sort class to current header
                        this.classList.add(`sort-${newSort}`);
                        const icon = this.querySelector('.sort-icon');
                        if (icon) {
                            icon.className =
                                `bx bx-chevron-${newSort === 'asc' ? 'up' : 'down'} sort-icon`;
                        }

                        // Perform sort
                        sortTable(sortField, newSort);
                    });
                });
            }

            // Sort table function
            function sortTable(field, direction) {
                const tbody = document.getElementById('tableBody');
                const rows = Array.from(tbody.querySelectorAll('tr'));

                rows.sort((a, b) => {
                    let aVal, bVal;

                    switch (field) {
                        case 'index':
                            aVal = parseInt(a.cells[0].textContent);
                            bVal = parseInt(b.cells[0].textContent);
                            break;
                        case 'date':
                            aVal = new Date(a.cells[1].textContent.split('/').reverse().join('-'));
                            bVal = new Date(b.cells[1].textContent.split('/').reverse().join('-'));
                            break;
                        case 'customer':
                            aVal = a.cells[2].textContent.toLowerCase();
                            bVal = b.cells[2].textContent.toLowerCase();
                            break;
                        case 'package':
                            aVal = a.cells[3].textContent.toLowerCase();
                            bVal = b.cells[3].textContent.toLowerCase();
                            break;
                        case 'amount':
                            aVal = parseInt(a.cells[4].textContent.replace(/[^\d]/g, ''));
                            bVal = parseInt(b.cells[4].textContent.replace(/[^\d]/g, ''));
                            break;
                        case 'due_date':
                            aVal = new Date(a.cells[5].textContent.split('/').reverse().join('-'));
                            bVal = new Date(b.cells[5].textContent.split('/').reverse().join('-'));
                            break;
                        case 'status':
                            aVal = a.cells[6].textContent.toLowerCase();
                            bVal = b.cells[6].textContent.toLowerCase();
                            break;
                        default:
                            aVal = a.cells[0].textContent;
                            bVal = b.cells[0].textContent;
                    }

                    if (direction === 'asc') {
                        return aVal > bVal ? 1 : -1;
                    } else {
                        return aVal < bVal ? 1 : -1;
                    }
                });

                // Clear tbody and append sorted rows
                tbody.innerHTML = '';
                rows.forEach(row => tbody.appendChild(row));
            }

            // Show notification function
            function showNotification(message, type = 'info') {
                // Create notification element
                const notification = document.createElement('div');
                notification.className =
                    `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
                notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

                document.body.appendChild(notification);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 5000);
            }

            // Initialize table sorting on page load
            initializeTableSorting();

            // Auto-refresh data every 30 seconds (optional)
            setInterval(() => {
                if (!searchInput.value && !statusFilter.value && !startDate.value && !endDate.value) {
                    performSearch();
                }
            }, 30000);
        });

        // Global functions for button actions
        function viewInvoice(id) {
            // Implement view invoice functionality
            window.open(`/invoice/${id}`, '_blank');
        }

        function printInvoice(id) {
            // Implement print invoice functionality
            window.open(`/invoice/${id}/print`, '_blank');
        }

        // Initialize tooltips
        function initializeTooltips() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // Initialize tooltips on page load
        initializeTooltips();

        // Add CSS for sorting icons and enhanced styling
        const sortingCSS = `
    .sortable {
        cursor: pointer;
        user-select: none;
        position: relative;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .sortable:hover {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
        color: #4f46e5 !important;
    }

    .sort-icon {
        font-size: 0.75rem;
        margin-left: 0.25rem;
        opacity: 0.4;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .sortable.sort-asc .sort-icon,
    .sortable.sort-desc .sort-icon {
        opacity: 1;
        color: #4f46e5;
    }

    .sortable.sort-desc .sort-icon {
        transform: rotate(180deg);
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%) !important;
    }

    .action-btn {
        border-width: 1.5px !important;
        font-weight: 600 !important;
    }

    .action-btn:hover {
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    }
`;

        // Inject sorting CSS
        const style = document.createElement('style');
        style.textContent = sortingCSS;
        document.head.appendChild(style);
    </script>
@endsection
