@extends('layouts.contentNavbarLayout')

@section('title', 'Data Pendapatan')

@section('content')
    <div class="container-fluid px-4 py-6">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 mb-2">Data Pendapatan</h1>
                    <p class="text-gray-600">Kelola dan pantau data pendapatan perusahaan</p>
                </div>
                <div class="flex items-center gap-3">
                    <button onclick="refreshData()"
                        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="bx bx-refresh mr-2"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Revenue -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 mb-1">Total Pendapatan</p>
                        <p class="text-2xl font-bold text-gray-900">Rp {{ number_format($totalRevenue ?? 0, 0, ',', '.') }}
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="bx bx-trending-up text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Monthly Revenue -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 mb-1">Pendapatan Bulan Ini</p>
                        <p class="text-2xl font-bold text-gray-900">Rp
                            {{ number_format($monthlyRevenue ?? 0, 0, ',', '.') }}</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="bx bx-calendar text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Pending Revenue -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 mb-1">Pendapatan Tertunda</p>
                        <p class="text-2xl font-bold text-gray-900">Rp
                            {{ number_format($pendingRevenue ?? 0, 0, ',', '.') }}</p>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="bx bx-time text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Invoices -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 mb-1">Total Invoice</p>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($totalInvoices ?? 0, 0, ',', '.') }}
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="bx bx-receipt text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <form id="filterForm" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Search Input -->
                    <div class="lg:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Pencarian</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="bx bx-search text-gray-400"></i>
                            </div>
                            <input type="text" id="searchInput" name="search" value="{{ $search ?? '' }}"
                                placeholder="Cari nama customer atau paket..."
                                class="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                        </div>
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select id="statusFilter" name="status"
                            class="block w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                            <option value="">Semua Status</option>
                            @foreach ($statusOptions ?? [] as $statusOption)
                                <option value="{{ $statusOption->id }}"
                                    {{ ($status ?? '') == $statusOption->id ? 'selected' : '' }}>
                                    {{ $statusOption->nama_status }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Date Range -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tanggal</label>
                        <div class="flex gap-2">
                            <input type="date" id="startDate" name="start_date" value="{{ $startDate ?? '' }}"
                                class="block w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                            <input type="date" id="endDate" name="end_date" value="{{ $endDate ?? '' }}"
                                class="block w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                        </div>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-3 pt-2">
                    <button type="button" onclick="applyFilters()"
                        class="inline-flex items-center justify-center px-4 py-2.5 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="bx bx-filter-alt mr-2"></i>
                        Terapkan Filter
                    </button>
                    <button type="button" onclick="clearFilters()"
                        class="inline-flex items-center justify-center px-4 py-2.5 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="bx bx-x mr-2"></i>
                        Reset Filter
                    </button>
                </div>
            </form>
        </div>

        <!-- Data Table -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Daftar Invoice</h3>
            </div>

            <div class="overflow-x-auto">
                <div id="tableContainer">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    No</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Tanggal</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Customer</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Paket</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Tagihan</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Jatuh Tempo</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Aksi</th>
                            </tr>
                        </thead>
                        <tbody id="tableBody" class="bg-white divide-y divide-gray-200">
                            @forelse($invoices ?? [] as $index => $invoice)
                                <tr class="hover:bg-gray-50 transition-colors">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ $invoices->firstItem() + $index }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ \Carbon\Carbon::parse($invoice->created_at)->format('d/m/Y') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div
                                                class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="bx bx-user text-blue-600"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ $invoice->customer->nama_customer ?? 'N/A' }}</div>
                                                <div class="text-sm text-gray-500">
                                                    {{ Str::limit($invoice->customer->alamat ?? '', 30) }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span
                                            class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ $invoice->paket->nama_paket ?? 'N/A' }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <i class="bx bx-money text-green-600 mr-2"></i>
                                            <span class="text-sm font-semibold text-green-600">
                                                Rp {{ number_format($invoice->tagihan, 0, ',', '.') }}
                                            </span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ \Carbon\Carbon::parse($invoice->jatuh_tempo)->format('d/m/Y') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if ($invoice->status)
                                            @if ($invoice->status->nama_status == 'Sudah Bayar')
                                                <span
                                                    class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="bx bx-check-circle mr-1"></i>
                                                    {{ $invoice->status->nama_status }}
                                                </span>
                                            @elseif($invoice->status->nama_status == 'Belum Bayar')
                                                <span
                                                    class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    <i class="bx bx-x-circle mr-1"></i>
                                                    {{ $invoice->status->nama_status }}
                                                </span>
                                            @else
                                                <span
                                                    class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    <i class="bx bx-time-five mr-1"></i>
                                                    {{ $invoice->status->nama_status }}
                                                </span>
                                            @endif
                                        @else
                                            <span
                                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                N/A
                                            </span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center gap-2">
                                            <button onclick="viewInvoice({{ $invoice->id }})"
                                                class="inline-flex items-center px-3 py-1.5 bg-blue-100 text-blue-700 text-xs font-medium rounded-lg hover:bg-blue-200 transition-colors">
                                                <i class="bx bx-show mr-1"></i>
                                                Lihat
                                            </button>
                                            @if ($invoice->status && $invoice->status->nama_status == 'Belum Bayar')
                                                <button onclick="processPayment({{ $invoice->id }})"
                                                    class="inline-flex items-center px-3 py-1.5 bg-green-100 text-green-700 text-xs font-medium rounded-lg hover:bg-green-200 transition-colors">
                                                    <i class="bx bx-money mr-1"></i>
                                                    Bayar
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="8" class="px-6 py-12 text-center">
                                        <div class="flex flex-col items-center">
                                            <i class="bx bx-receipt text-gray-400 text-4xl mb-4"></i>
                                            <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak ada data</h3>
                                            <p class="text-gray-500">Belum ada data invoice yang tersedia</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            @if (isset($invoices) && $invoices->hasPages())
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div class="text-sm text-gray-700">
                            Menampilkan {{ $invoices->firstItem() ?? 0 }} sampai {{ $invoices->lastItem() ?? 0 }}
                            dari {{ $invoices->total() ?? 0 }} hasil
                        </div>
                        <div class="flex items-center gap-2">
                            {{ $invoices->appends(request()->query())->links('pagination::tailwind') }}
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 flex items-center gap-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span class="text-gray-700">Memuat data...</span>
        </div>
    </div>

@endsection

@section('page-script')
    <script>
        let searchTimeout;
        let isLoading = false;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeSearch();
            initializeFilters();
        });

        // Initialize search functionality
        function initializeSearch() {
            const searchInput = document.getElementById('searchInput');

            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        applyFilters();
                    }, 500); // Debounce search for 500ms
                });
            }
        }

        // Initialize filter functionality
        function initializeFilters() {
            const statusFilter = document.getElementById('statusFilter');
            const startDate = document.getElementById('startDate');
            const endDate = document.getElementById('endDate');

            [statusFilter, startDate, endDate].forEach(element => {
                if (element) {
                    element.addEventListener('change', applyFilters);
                }
            });
        }

        // Apply filters and search
        function applyFilters() {
            if (isLoading) return;

            const formData = new FormData(document.getElementById('filterForm'));
            const params = new URLSearchParams();

            for (let [key, value] of formData.entries()) {
                if (value.trim() !== '') {
                    params.append(key, value);
                }
            }

            // Show loading
            showLoading();

            // Make AJAX request
            fetch(`{{ route('pendapatan') }}?${params.toString()}`, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'text/html'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.text();
                })
                .then(html => {
                    // Parse the response and update the table
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');

                    // Update table content
                    const newTableContainer = doc.querySelector('#tableContainer');
                    const currentTableContainer = document.querySelector('#tableContainer');

                    if (newTableContainer && currentTableContainer) {
                        currentTableContainer.innerHTML = newTableContainer.innerHTML;
                    }

                    // Update pagination if exists
                    const newPagination = doc.querySelector('.px-6.py-4.border-t');
                    const currentPagination = document.querySelector('.px-6.py-4.border-t');

                    if (newPagination && currentPagination) {
                        currentPagination.innerHTML = newPagination.innerHTML;
                    }

                    // Update URL without page reload
                    const url = new URL(window.location);
                    for (let [key, value] of params.entries()) {
                        url.searchParams.set(key, value);
                    }

                    // Remove empty parameters
                    for (let key of url.searchParams.keys()) {
                        if (!params.has(key)) {
                            url.searchParams.delete(key);
                        }
                    }

                    window.history.pushState({}, '', url);
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Terjadi kesalahan saat memuat data', 'error');
                })
                .finally(() => {
                    hideLoading();
                });
        }

        // Clear all filters
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('startDate').value = '';
            document.getElementById('endDate').value = '';

            // Redirect to clean URL
            window.location.href = '{{ route('pendapatan') }}';
        }

        // Refresh data
        function refreshData() {
            if (isLoading) return;

            showLoading();

            // Get current filters
            const formData = new FormData(document.getElementById('filterForm'));
            const params = new URLSearchParams();

            for (let [key, value] of formData.entries()) {
                if (value.trim() !== '') {
                    params.append(key, value);
                }
            }

            // Reload with current filters
            window.location.href = `{{ route('pendapatan') }}?${params.toString()}`;
        }

        // View invoice details
        function viewInvoice(invoiceId) {
            // You can implement modal or redirect to detail page
            window.location.href = `/invoice/${invoiceId}`;
        }

        // Process payment
        function processPayment(invoiceId) {
            if (confirm('Apakah Anda yakin ingin memproses pembayaran ini?')) {
                // You can implement payment processing logic here
                window.location.href = `/payment/${invoiceId}`;
            }
        }

        // Show loading overlay
        function showLoading() {
            isLoading = true;
            document.getElementById('loadingOverlay').classList.remove('hidden');
        }

        // Hide loading overlay
        function hideLoading() {
            isLoading = false;
            document.getElementById('loadingOverlay').classList.add('hidden');
        }

        // Show notification
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'success' ? 'bg-green-500 text-white' :
            'bg-blue-500 text-white'
        }`;

            notification.innerHTML = `
            <div class="flex items-center gap-3">
                <i class="bx ${
                    type === 'error' ? 'bx-error' :
                    type === 'success' ? 'bx-check' :
                    'bx-info-circle'
                }"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-auto">
                    <i class="bx bx-x"></i>
                </button>
            </div>
        `;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Handle pagination clicks
        document.addEventListener('click', function(e) {
            if (e.target.closest('.pagination a')) {
                e.preventDefault();
                const link = e.target.closest('.pagination a');
                const url = new URL(link.href);

                // Get current form data
                const formData = new FormData(document.getElementById('filterForm'));

                // Add form data to pagination URL
                for (let [key, value] of formData.entries()) {
                    if (value.trim() !== '') {
                        url.searchParams.set(key, value);
                    }
                }

                showLoading();
                window.location.href = url.toString();
            }
        });

        // Auto-refresh every 30 seconds (optional)
        setInterval(() => {
            if (!isLoading && document.visibilityState === 'visible') {
                // Silently refresh data in background
                applyFilters();
            }
        }, 30000);
    </script>
@endsection
